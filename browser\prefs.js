// Mozilla User Preferences

// DO NOT EDIT THIS FILE.
//
// If you make changes to this file while the application is running,
// the changes will be overwritten when the application exits.
//
// To change a preference value, you can either:
// - modify it via the UI (e.g. via about:config in the browser); or
// - set it within a user.js file in your profile.

user_pref("app.normandy.api_url", "");
user_pref("app.normandy.enabled", false);
user_pref("app.normandy.first_run", false);
user_pref("app.normandy.migrationsApplied", 12);
user_pref("app.shield.optoutstudies.enabled", false);
user_pref("app.update.auto", false);
user_pref("app.update.auto.migrated", true);
user_pref("app.update.checkInstallTime", false);
user_pref("app.update.disabledForTesting", true);
user_pref("app.update.enabled", false);
user_pref("app.update.lastUpdateTime.addon-background-update-timer", 1749101476);
user_pref("app.update.lastUpdateTime.background-update-timer", 1749101476);
user_pref("app.update.lastUpdateTime.browser-cleanup-thumbnails", 1749119149);
user_pref("app.update.lastUpdateTime.region-update-timer", 1748528848);
user_pref("app.update.lastUpdateTime.services-settings-poll-changes", 1749101476);
user_pref("app.update.lastUpdateTime.telemetry_modules_ping", 1748601826);
user_pref("app.update.lastUpdateTime.telemetry_untrustedmodules_ping", 0);
user_pref("app.update.lastUpdateTime.xpi-signature-verification", 1749101476);
user_pref("app.update.migrated.updateDir3.AB3AC84070DF7FDF", true);
user_pref("app.update.migrated.updateDir3.F1735EAC7F88CE1E", true);
user_pref("app.update.mode", 0);
user_pref("app.update.service.enabled", false);
user_pref("apz.content_response_timeout", 60000);
user_pref("browser.bookmarks.addedImportButton", true);
user_pref("browser.bookmarks.restore_default_bookmarks", false);
user_pref("browser.contentblocking.category", "custom");
user_pref("browser.contextual-services.contextId", "{9d702a1e-cb7e-41f5-9067-e23178966fcf}");
user_pref("browser.download.panel.shown", true);
user_pref("browser.download.viewableInternally.typeWasRegistered.avif", true);
user_pref("browser.download.viewableInternally.typeWasRegistered.webp", true);
user_pref("browser.launcherProcess.enabled", true);
user_pref("browser.library.activity-stream.enabled", false);
user_pref("browser.migration.version", 143);
user_pref("browser.newtabpage.activity-stream.feeds.section.topstories", false);
user_pref("browser.newtabpage.activity-stream.feeds.topsites", false);
user_pref("browser.newtabpage.activity-stream.impressionId", "{bb67d4db-8942-4c3b-9b09-b5a725c38e83}");
user_pref("browser.newtabpage.activity-stream.showSponsoredTopSites", false);
user_pref("browser.newtabpage.enabled", false);
user_pref("browser.newtabpage.storageVersion", 1);
user_pref("browser.pageActions.persistedActions", "{\"ids\":[\"bookmark\"],\"idsInUrlbar\":[\"bookmark\"],\"idsInUrlbarPreProton\":[],\"version\":1}");
user_pref("browser.pagethumbnails.capturing_disabled", true);
user_pref("browser.pagethumbnails.storage_version", 3);
user_pref("browser.privacySegmentation.createdShortcut", true);
user_pref("browser.proton.toolbar.version", 3);
user_pref("browser.region.network.url", "");
user_pref("browser.region.update.updated", **********);
user_pref("browser.safebrowsing.blockedURIs.enabled", false);
user_pref("browser.safebrowsing.downloads.enabled", false);
user_pref("browser.safebrowsing.malware.enabled", false);
user_pref("browser.safebrowsing.passwords.enabled", false);
user_pref("browser.safebrowsing.phishing.enabled", false);
user_pref("browser.safebrowsing.provider.mozilla.updateURL", "");
user_pref("browser.search.geoSpecificDefaults", false);
user_pref("browser.search.geoSpecificDefaults.url", "");
user_pref("browser.search.region", "IN");
user_pref("browser.search.update", false);
user_pref("browser.sessionstore.resume_from_crash", false);
user_pref("browser.sessionstore.upgradeBackup.latestBuildID", "20240419165035");
user_pref("browser.shell.checkDefaultBrowser", false);
user_pref("browser.startup.couldRestoreSession.count", 2);
user_pref("browser.startup.homepage", "about:blank");
user_pref("browser.startup.homepage_override.mstone", "ignore");
user_pref("browser.startup.lastColdStartupCheck", **********);
user_pref("browser.startup.page", 0);
user_pref("browser.tabs.disableBackgroundZombification", false);
user_pref("browser.tabs.warnOnCloseOtherTabs", false);
user_pref("browser.tabs.warnOnOpen", false);
user_pref("browser.topsites.contile.enabled", false);
user_pref("browser.topsites.contile.lastFetch", **********);
user_pref("browser.translations.enable", false);
user_pref("browser.uiCustomization.state", "{\"placements\":{\"widget-overflow-fixed-list\":[],\"unified-extensions-area\":[],\"nav-bar\":[\"back-button\",\"forward-button\",\"stop-reload-button\",\"customizableui-special-spring1\",\"urlbar-container\",\"customizableui-special-spring2\",\"save-to-pocket-button\",\"downloads-button\",\"fxa-toolbar-menu-button\",\"unified-extensions-button\",\"reset-pbm-toolbar-button\"],\"toolbar-menubar\":[\"menubar-items\"],\"TabsToolbar\":[\"firefox-view-button\",\"tabbrowser-tabs\",\"new-tab-button\",\"alltabs-button\"],\"PersonalToolbar\":[\"import-button\",\"personal-bookmarks\"]},\"seen\":[\"save-to-pocket-button\",\"developer-button\"],\"dirtyAreaCache\":[\"nav-bar\",\"PersonalToolbar\",\"toolbar-menubar\",\"TabsToolbar\"],\"currentVersion\":20,\"newElementCount\":2}");
user_pref("browser.uitour.enabled", false);
user_pref("browser.urlbar.placeholderName", "Google");
user_pref("browser.urlbar.quicksuggest.migrationVersion", 2);
user_pref("browser.urlbar.quicksuggest.scenario", "history");
user_pref("browser.urlbar.suggest.searches", false);
user_pref("browser.usedOnWindows10.introURL", "");
user_pref("browser.warnOnQuit", false);
user_pref("captivedetect.canonicalURL", "");
user_pref("datareporting.healthreport.about.reportUrl", "");
user_pref("datareporting.healthreport.documentServerURI", "");
user_pref("datareporting.healthreport.logging.consoleEnabled", false);
user_pref("datareporting.healthreport.service.enabled", false);
user_pref("datareporting.healthreport.service.firstRun", false);
user_pref("datareporting.healthreport.uploadEnabled", false);
user_pref("datareporting.policy.dataSubmissionEnabled", false);
user_pref("datareporting.policy.dataSubmissionPolicyBypassNotification", true);
user_pref("devtools.jsonview.enabled", false);
user_pref("devtools.toolbox.host", "window");
user_pref("distribution.iniFile.exists.appversion", "125.0.1");
user_pref("distribution.iniFile.exists.value", false);
user_pref("doh-rollout.doneFirstRun", true);
user_pref("doh-rollout.home-region", "IN");
user_pref("dom.disable_open_during_load", false);
user_pref("dom.file.createInChild", true);
user_pref("dom.iframe_lazy_loading.enabled", false);
user_pref("dom.input_events.security.minNumTicks", 0);
user_pref("dom.input_events.security.minTimeElapsedInMS", 0);
user_pref("dom.ipc.processCount", 60000);
user_pref("dom.ipc.processPrelaunch.enabled", false);
user_pref("dom.ipc.reportProcessHangs", false);
user_pref("dom.max_script_run_time", 0);
user_pref("dom.postMessage.sharedArrayBuffer.bypassCOOP_COEP.insecure.enabled", true);
user_pref("dom.push.connection.enabled", false);
user_pref("dom.push.serverURL", "");
user_pref("extensions.activeThemeID", "<EMAIL>");
user_pref("extensions.autoDisableScopes", 0);
user_pref("extensions.blocklist.enabled", false);
user_pref("extensions.blocklist.pingCountVersion", -1);
user_pref("extensions.databaseSchema", 35);
user_pref("extensions.enabledScopes", 5);
user_pref("extensions.formautofill.addresses.supported", "off");
user_pref("extensions.formautofill.creditCards.supported", "off");
user_pref("extensions.getAddons.cache.enabled", false);
user_pref("extensions.installDistroAddons", false);
user_pref("extensions.lastAppBuildId", "20240419165035");
user_pref("extensions.lastAppVersion", "125.0.1");
user_pref("extensions.lastPlatformVersion", "125.0.1");
user_pref("extensions.pendingOperations", false);
user_pref("extensions.pictureinpicture.enable_picture_in_picture_overrides", true);
user_pref("extensions.pocket.enabled", false);
user_pref("extensions.screenshots.disabled", true);
user_pref("extensions.screenshots.upload-disabled", true);
user_pref("extensions.systemAddonSet", "{\"schema\":1,\"addons\":{}}");
user_pref("extensions.update.enabled", false);
user_pref("extensions.update.notifyUser", false);
user_pref("extensions.webcompat.enable_shims", true);
user_pref("extensions.webcompat.perform_injections", true);
user_pref("extensions.webcompat.perform_ua_overrides", true);
user_pref("<EMAIL>", true);
user_pref("extensions.webextensions.uuids", "{\"<EMAIL>\":\"530b77e2-5b60-4093-80fa-9d440670aa45\",\"<EMAIL>\":\"21e211bb-6265-4656-81e9-ce5fb0cfaa0e\",\"<EMAIL>\":\"8270ba4f-e6a8-4e6c-8f23-23196c3eb3e5\",\"<EMAIL>\":\"07aa62af-78a2-49aa-9ae0-ccb92171eda0\",\"<EMAIL>\":\"3d795e40-aa0a-4569-945b-0998511417ee\",\"<EMAIL>\":\"4f19db0d-ab7f-4c61-acd3-4275184a0298\",\"<EMAIL>\":\"03f22697-8a56-4109-bc99-654335dd3272\",\"<EMAIL>\":\"524e1e7c-f1f7-4240-be69-5a2cdd3a5ddd\",\"<EMAIL>\":\"5174bd21-b689-4882-b61d-22acabdd7ab1\",\"<EMAIL>\":\"4a2ba1cc-db57-46d0-9460-4a131c12c7df\",\"<EMAIL>\":\"7da411be-2ea3-42e5-abc6-2eaed953e9da\"}");
user_pref("extensions.webservice.discoverURL", "");
user_pref("fission.bfcacheInParent", false);
user_pref("fission.webContentIsolationStrategy", 0);
user_pref("focusmanager.testmode", true);
user_pref("gecko.handlerService.defaultHandlersVersion", 1);
user_pref("general.useragent.updates.enabled", false);
user_pref("geo.provider.testing", true);
user_pref("geo.wifi.scan", false);
user_pref("gfx.color_management.mode", 0);
user_pref("gfx.color_management.rendering_intent", 3);
user_pref("hangmonitor.timeout", 0);
user_pref("javascript.options.showInConsole", true);
user_pref("layout.spellcheckDefault", 0);
user_pref("media.gmp-manager.buildID", "20240419165035");
user_pref("media.gmp-manager.lastCheck", **********);
user_pref("media.gmp-manager.lastEmptyCheck", **********);
user_pref("media.gmp-manager.updateEnabled", false);
user_pref("media.gmp.storage.version.observed", 1);
user_pref("media.hardware-video-decoding.failed", false);
user_pref("network.captive-portal-service.enabled", false);
user_pref("network.connectivity-service.enabled", false);
user_pref("network.cookie.cookieBehavior", 4);
user_pref("network.http.phishy-userpass-length", 255);
user_pref("network.http.speculative-parallel-limit", 0);
user_pref("network.manage-offline-status", false);
user_pref("network.sntp.pools", "");
user_pref("pdfjs.disabled", true);
user_pref("pdfjs.enabledCache.state", false);
user_pref("permissions.isolateBy.userContext", true);
user_pref("plugin.state.flash", 0);
user_pref("privacy.sanitize.pending", "[]");
user_pref("prompts.contentPromptSubDialog", false);
user_pref("sanity-test.device-id", "0x0a16");
user_pref("sanity-test.driver-version", "20.19.15.4531");
user_pref("sanity-test.running", false);
user_pref("sanity-test.version", "20240419165035");
user_pref("security.certerrors.mitm.priming.enabled", false);
user_pref("security.fileuri.strict_origin_policy", false);
user_pref("security.notification_enable_delay", 0);
user_pref("services.settings.server", "");
user_pref("signon.autofillForms", false);
user_pref("signon.rememberSignons", false);
user_pref("startup.homepage_welcome_url", "about:blank");
user_pref("toolkit.cosmeticAnimations.enabled", false);
user_pref("toolkit.shutdown.fastShutdownStage", 3);
user_pref("toolkit.startup.last_success", 1749119164);
user_pref("toolkit.startup.max_resumed_crashes", -1);
user_pref("toolkit.telemetry.cachedClientID", "c0ffeec0-ffee-c0ff-eec0-ffeec0ffeec0");
user_pref("toolkit.telemetry.enabled", false);
user_pref("toolkit.telemetry.previousBuildID", "20240419165035");
user_pref("toolkit.telemetry.reportingpolicy.firstRun", false);
user_pref("toolkit.telemetry.server", "");
user_pref("ui.systemUsesDarkTheme", 0);
user_pref("ui.use_standins_for_native_colors", true);
user_pref("webgl.forbid-software", false);
